using UnityEngine;

[CreateAssetMenu]
public class ScriptableStats : ScriptableObject
{
    [Head<PERSON>("LAYERS")]
    [Tooltip("Set this to the layer your player is on")]
    public LayerMask PlayerLayer;

    [Header("TAGS")]
    [Tooltip("Set this to the tag your player is on")]
    public string PlayerTag = "Player";

    [Header("INPUT")]
    [Tooltip(
        "Makes all Input snap to an integer. Prevents gamepads from walking slowly. Recommended value is true to ensure gamepad/keybaord parity."
    )]
    public bool SnapInput = true;

    [
        Tooltip(
            "Minimum input required before you mount a ladder or climb a ledge. Avoids unwanted climbing using controllers"
        ),
        Range(0.01f, 0.99f)
    ]
    public float VerticalDeadZoneThreshold = 0.3f;

    [
        Tooltip(
            "Minimum input required before a left or right is recognized. Avoids drifting with sticky controllers"
        ),
        Range(0.01f, 0.99f)
    ]
    public float HorizontalDeadZoneThreshold = 0.1f;

    [<PERSON><PERSON>("MOVEMENT")]
    [Tooltip("The top horizontal movement speed")]
    public float MaxSpeed = 14;

    [Tooltip("The player's capacity to gain horizontal speed")]
    public float Acceleration = 120;

    [Tooltip("The pace at which the player comes to a stop")]
    public float GroundDeceleration = 60;

    [Tooltip("Deceleration in air only after stopping input mid-air")]
    public float AirDeceleration = 30;

    [Tooltip("A constant downward force applied while grounded. Helps on slopes"), Range(0f, -10f)]
    public float GroundingForce = -1.5f;

    [Tooltip("The detection distance for grounding and roof detection"), Range(0f, 0.5f)]
    public float GrounderDistance = 0.05f;

    [Header("JUMP")]
    [Tooltip("The immediate velocity applied when jumping")]
    public float JumpPower = 36;

    [Tooltip("The maximum vertical movement speed")]
    public float MaxFallSpeed = 40;

    [Tooltip("The player's capacity to gain fall speed. a.k.a. In Air Gravity")]
    public float FallAcceleration = 110;

    [Tooltip("The gravity multiplier added when jump is released early")]
    public float JumpEndEarlyGravityModifier = 3;

    [Tooltip(
        "The time before coyote jump becomes unusable. Coyote jump allows jump to execute even after leaving a ledge"
    )]
    public float CoyoteTime = .15f;

    [Tooltip(
        "The amount of time we buffer a jump. This allows jump input before actually hitting the ground"
    )]
    public float JumpBuffer = .2f;

    [Header("DASH")]
    [Tooltip("The speed applied during dash")]
    public float DashPower = 50f;

    [Tooltip("The duration of the dash in seconds")]
    public float DashDuration = 0.2f;

    [Tooltip("The maximum dash distance")]
    public float DashDistance = 3f;

    [Header("CROUCH")]
    [Tooltip("Vertical height reduction when crouching (in world units)")]
    [Range(0f, 2f)]
    public float CrouchHeightReduction = 0.5f;

    [Header("SWIM")]
    [Tooltip("Movement speed while swimming")]
    public float SwimSpeed = 5f;

    [Tooltip("The upward force applied when submerged")]
    public float Buoyancy = 5f;

    [Tooltip("The drag applied when not moving in water")]
    public float SwimDrag = 2f;

    [Tooltip("How quickly the player rotates while swimming")]
    public float SwimRotationSpeed = 10f;

    [Header("GLIDING")]
    public float GlideFallSpeed = 2f;

    // NEW: Speed of forward movement while gliding
    public float GlideForwardSpeed = 4f;

    // NEW: Angle in degrees to tilt the player while gliding
    public float GlideTiltAngle = 15f;

    // NEW: How quickly the player rotates into the glide tilt
    public float GlideRotationSpeed = 10f;

    [Header("ROPE")]
    [Tooltip("Movement speed while climbing")]
    public float ClimbSpeed = 5f;

    [Tooltip("The immediate velocity applied when jumping from a rope")]
    public Vector2 RopeJumpPower = new Vector2(20, 20);

    [Tooltip("The cooldown in seconds before the rope can be used again after reaching the top")]
    public float RopeCooldown = 0.5f;



    [Header("WALL CLIMBING")]
    [Tooltip("Duration in seconds the player can grip a wall before sliding")]
    public float WallGripDuration = 1f;

    [Tooltip("The force applied when wall jumping")]
    public Vector2 WallJumpForce = new Vector2(15f, 25f);

    [Tooltip("Distance to check for walls on either side of the player")]
    public float WallCheckDistance = 0.6f;

    [Tooltip("Speed at which player slides down the wall after grip expires")]
    public float WallSlideSpeed = 3f;

    [Tooltip("LayerMask for climbable walls")]
    public LayerMask WallLayer = -1;
}
