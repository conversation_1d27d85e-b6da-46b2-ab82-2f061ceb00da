using System.Collections;
using UnityEngine;

public enum RopeDirection
{
    Left,
    Right,
    Both,
}

[RequireComponent(typeof(Collider2D))]
public class Rope : MonoBehaviour
{
    [Header("Rope Behavior")]
    public RopeDirection Direction = RopeDirection.Both;
    public Vector2 ExitForce = new(5, 5);

    [Toolt<PERSON>("The y-offset from the top of the rope's collider to set the exit point.")]
    public float ExitPointYOffset = 0.5f;
    public Vector2 PlayerExitPosition = new(0, 1);

    [Header("Latching")]
    public Vector2 TopLatchPointOffset = new(0, -1);

    private Collider2D _collider;
    private bool _isOnCooldown;

    public Vector2 WorldPlayerExitPoint => (Vector2)transform.position + PlayerExitPosition;

    public Vector2 TopLatchPoint => (Vector2)_collider.bounds.max + TopLatchPointOffset;

    private void Awake()
    {
        _collider = GetComponent<Collider2D>();
    }

    public void StartCooldown(float duration)
    {
        StartCoroutine(Cooldown(duration));
    }

    private void OnTriggerEnter2D(Collider2D collision)
    {
        if (_isOnCooldown)
            return;

        var player = collision.GetComponent<PlayerController>();
        if (player == null || player._stats == null)
            return;

        if (collision.CompareTag(player._stats.PlayerTag))
        {
            player.SetClimbing(true, this);
        }
    }

    private void OnTriggerExit2D(Collider2D collision)
    {
        var player = collision.GetComponent<PlayerController>();
        if (player == null)
            return;

        if (collision.CompareTag(player._stats.PlayerTag))
        {
            player.SetClimbing(false, null);
        }
    }

    private IEnumerator Cooldown(float duration)
    {
        _isOnCooldown = true;
        yield return new WaitForSeconds(duration);
        _isOnCooldown = false;
    }

    private void OnDrawGizmos()
    {
        // Draw interaction points
        if (_collider == null)
            _collider = GetComponent<Collider2D>();

        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(WorldPlayerExitPoint, 0.2f);

        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(TopLatchPoint, 0.2f);

        // Draw trigger area
        if (_collider != null)
        {
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireCube(_collider.bounds.center, _collider.bounds.size);
        }
    }
}
