using System.Collections;
using UnityEngine;

public class GrappleHook : MonoBehaviour
{
    [Header("Grapple Settings")]
    public float grappleRange = 10f;
    public float grappleSpeed = 20f;
    public float grappleCooldown = 1f;
    public float reelInSpeed = 8f; // Speed at which player is reeled in
    public LayerMask grappleableLayers = -1;
    
    [Header("References")]
    public LineRenderer lineRenderer;
    public Transform firePoint;
    
    private Rigidbody2D rb;
    private Camera cam;
    private Vector2 grapplePoint;
    private SpringJoint2D springJoint;
    private bool isGrappling = false;
    private float lastGrappleTime = -1f; // For cooldown
    private float originalGravityScale;
    
    void Start()
    {
        rb = GetComponent<Rigidbody2D>();
        cam = Camera.main;
        
        // Setup LineRenderer if not assigned
        if (lineRenderer == null)
        {
            lineRenderer = gameObject.AddComponent<LineRenderer>();
            lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
            lineRenderer.startColor = Color.white;
            lineRenderer.endColor = Color.white;
            lineRenderer.startWidth = 0.1f;
            lineRenderer.endWidth = 0.1f;
            lineRenderer.sortingOrder = 1;
        }
        
        lineRenderer.enabled = false;
        
        // Setup fire point if not assigned
        if (firePoint == null)
        {
            firePoint = transform;
        }
    }
    
    void Update()
    {
        // Input handling with cooldown
        if (Input.GetMouseButtonDown(0) && !isGrappling)
        {
            if (Time.time > lastGrappleTime + grappleCooldown)
            {
                StartGrapple();
            }
        }
        
        // Update line renderer
        if (isGrappling && lineRenderer.enabled)
        {
            lineRenderer.SetPosition(0, firePoint.position);
            lineRenderer.SetPosition(1, grapplePoint);
        }
    }

    void FixedUpdate()
    {
        if (isGrappling)
        {
            // Reel the player in by shortening the spring joint distance
            springJoint.distance = Mathf.Max(0f, springJoint.distance - reelInSpeed * Time.fixedDeltaTime);

            // Automatically release when the player gets close to the grapple point or the rope is fully reeled in
            if (Vector2.Distance(rb.position, grapplePoint) < 1.5f || springJoint.distance <= 0.1f) // Release threshold
            {
                StopGrapple();
            }
        }
    }
    
    void StartGrapple()
    {
        // Get mouse position in world space
        Vector2 mousePos = cam.ScreenToWorldPoint(Input.mousePosition);
        Vector2 direction = (mousePos - (Vector2)firePoint.position).normalized;
        
        // Perform raycast to find grapple point
        RaycastHit2D hit = Physics2D.Raycast(firePoint.position, direction, grappleRange, grappleableLayers);
        
        if (hit.collider != null)
        {
            grapplePoint = hit.point;
            isGrappling = true;
            
            // --- Attach Phase ---
            springJoint = gameObject.AddComponent<SpringJoint2D>();
            springJoint.autoConfigureDistance = false;
            springJoint.distance = Vector2.Distance(rb.worldCenterOfMass, grapplePoint);
            springJoint.connectedAnchor = grapplePoint;
            springJoint.frequency = 4.5f;
            springJoint.dampingRatio = 0.7f;
            springJoint.connectedBody = null; // Connect to world point
            springJoint.enableCollision = true; // Ensure player collider remains active

            originalGravityScale = rb.gravityScale;
            rb.gravityScale = originalGravityScale * 0.5f; // Reduce gravity for better swing
            
            // Calculate distance and set spring distance
            float distanceFromPoint = Vector2.Distance(firePoint.position, grapplePoint);
            springJoint.distance = distanceFromPoint * 0.8f;
            springJoint.autoConfigureDistance = false;
            
            // Enable line renderer
            lineRenderer.enabled = true;
            lineRenderer.positionCount = 2;
            
            // Remove the initial pull force to allow for a natural swing based on momentum
            // Vector2 pullDirection = (grapplePoint - (Vector2)transform.position).normalized;
            // rb.AddForce(pullDirection * grappleSpeed, ForceMode2D.Impulse);
        }
    }
    
    void StopGrapple()
    {
        if (isGrappling)
        {
            isGrappling = false;
            lineRenderer.enabled = false;
            
            if (springJoint != null)
            {
                Destroy(springJoint);
            }

            // Restore original gravity scale immediately
            rb.gravityScale = originalGravityScale;

            // Start cooldown timer
            lastGrappleTime = Time.time;
        }
    }

    void OnDrawGizmosSelected()
    {
        // Draw grapple range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, grappleRange);
        
        // Draw grapple point when grappling
        if (isGrappling)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(grapplePoint, 0.5f);
        }
    }
}