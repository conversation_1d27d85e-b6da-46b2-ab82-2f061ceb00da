using UnityEngine;

[RequireComponent(typeof(Collider2D))]
public class RopeExitZone : MonoBehaviour
{
    [SerializeField]
    private Rope _rope;

    private void Awake()
    {
        if (GetComponent<Collider2D>() != null)
        {
            GetComponent<Collider2D>().isTrigger = true;
        }
    }

    private void OnTriggerEnter2D(Collider2D collision)
    {
        var player = collision.GetComponent<PlayerController>();
        if (player != null && _rope != null && player.IsClimbingOnRope(_rope))
        {
            player.ExitRope(_rope);
        }
    }
}
