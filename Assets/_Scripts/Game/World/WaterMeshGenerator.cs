using UnityEngine;

// This ensures that whenever you add this script to a GameObject,
// Unity will automatically add a MeshFilter and a MeshRenderer as well.
[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
public class WaterMeshGenerator : MonoBehaviour
{
    [Header("Water Dimensions")]
    [Tooltip("The total width of the water body.")]
    public float width = 20f;

    [Tooltip("The total height of the water body.")]
    public float height = 2f;

    [Head<PERSON>("Mesh Quality")]
    [Tooltip("Number of horizontal segments. More segments = smoother waves.")]
    [Range(1, 200)]
    public int horizontalSubdivisions = 50;

    private MeshFilter meshFilter;

    // This is the public method that the editor script will call.
    public void GenerateMesh()
    {
        // Get the MeshFilter component, creating it if it doesn't exist.
        meshFilter = GetComponent<MeshFilter>();
        if (meshFilter == null)
        {
            meshFilter = gameObject.AddComponent<MeshFilter>();
        }

        // Create a new mesh or clear the existing one.
        Mesh mesh = new Mesh();
        mesh.name = "GeneratedWaterMesh";

        // --- Calculate Vertex Positions and UVs ---

        // The number of vertices is the number of subdivisions + 1 (for the last vertex),
        // multiplied by 2 because we have a top and a bottom edge.
        int vertexCount = (horizontalSubdivisions + 1) * 2;
        Vector3[] vertices = new Vector3[vertexCount];
        Vector2[] uvs = new Vector2[vertexCount];

        // The pivot is at the bottom-left corner.
        Vector3 startPoint = -new Vector3(width / 2f, 0, 0);

        for (int i = 0; i <= horizontalSubdivisions; i++)
        {
            float xProgress = (float)i / horizontalSubdivisions;

            // Bottom vertex for this segment
            int bottomVertexIndex = i * 2;
            vertices[bottomVertexIndex] = startPoint + new Vector3(xProgress * width, 0, 0);
            uvs[bottomVertexIndex] = new Vector2(xProgress, 0);

            // Top vertex for this segment
            int topVertexIndex = i * 2 + 1;
            vertices[topVertexIndex] = startPoint + new Vector3(xProgress * width, height, 0);
            uvs[topVertexIndex] = new Vector2(xProgress, 1);
        }

        // --- Calculate Triangles ---

        // Each subdivision creates a quad, which is two triangles (6 indices).
        int[] triangles = new int[horizontalSubdivisions * 6];

        for (int i = 0; i < horizontalSubdivisions; i++)
        {
            int quadStartIndex = i * 6;
            int vertexStartIndex = i * 2;

            // First triangle of the quad
            triangles[quadStartIndex + 0] = vertexStartIndex; // Bottom-left
            triangles[quadStartIndex + 1] = vertexStartIndex + 1; // Top-left
            triangles[quadStartIndex + 2] = vertexStartIndex + 3; // Top-right

            // Second triangle of the quad
            triangles[quadStartIndex + 3] = vertexStartIndex; // Bottom-left
            triangles[quadStartIndex + 4] = vertexStartIndex + 3; // Top-right
            triangles[quadStartIndex + 5] = vertexStartIndex + 2; // Bottom-right
        }

        // --- Assign data to the mesh ---
        mesh.vertices = vertices;
        mesh.triangles = triangles;
        mesh.uv = uvs;
        mesh.RecalculateNormals(); // Important for lighting/shader effects
        mesh.RecalculateBounds(); // Important for visibility culling

        // Assign the newly created mesh to the MeshFilter.
        meshFilter.mesh = mesh;

        // --- Add or Update BoxCollider2D for 2D Physics ---
        BoxCollider2D boxCollider2D = GetComponent<BoxCollider2D>();
        if (boxCollider2D == null)
        {
            boxCollider2D = gameObject.AddComponent<BoxCollider2D>();
        }

        // The local offset of the 2D collider is at half its height.
        boxCollider2D.offset = new Vector2(0, height / 2f);
        // The size should match the 2D dimensions of the mesh.
        boxCollider2D.size = new Vector2(width, height);

        Debug.Log("Water mesh and BoxCollider2D generated successfully!");
    }

    // This will draw a visual guide in the Scene view so you can see
    // the dimensions of your water body without a mesh being generated yet.
    private void OnDrawGizmos()
    {
        // By setting the gizmo's matrix to the object's local-to-world matrix,
        // we can draw the gizmo in local space, ensuring it aligns with the generated mesh
        // regardless of the object's rotation or parent transforms.
        Gizmos.matrix = transform.localToWorldMatrix;
        Gizmos.color = new Color(0, 0.8f, 1f, 0.5f); // A nice transparent blue

        // The center of the gizmo in local space. The mesh is generated from y=0 to y=height,
        // so its local center is at height/2.
        Vector3 center = new Vector3(0, height / 2f, 0);
        Vector3 size = new Vector3(width, height, 0.1f); // Give it a little depth for visibility
        Gizmos.DrawWireCube(center, size);
    }
}
