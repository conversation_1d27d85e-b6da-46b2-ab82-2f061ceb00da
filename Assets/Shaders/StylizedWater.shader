// Made for Unity Universal Render Pipeline (URP) - CORRECTED VERSION
Shader "Custom/WaterShader"
{
    Properties
    {
        [Header(Base Settings)]
        _BaseColor("Base Color", Color) = (0.2, 0.5, 1, 0.7)
        
        [Header(Wave Motion)]
        _WaveAmplitude("Wave Amplitude", Range(0, 0.2)) = 0.05
        _WaveFrequency("Wave Frequency", Range(0, 5)) = 2.0
        _WaveSpeed("Wave Speed", Range(0, 5)) = 1.5

        [Header(Caustics)]
        _CausticTex("Caustics Texture (Grayscale)", 2D) = "white" {}
        _CausticStrength("Caustic Strength", Range(0, 2)) = 0.5
        _CausticSpeed("Caustic Speed", Vector) = (0.03, 0.02, 0, 0)

        [Header(Ripple Interaction)]
        _RippleCenter("Ripple Center (World Pos)", Vector) = (0,0,0,0)
        _RippleStrength("Ripple Strength", Range(0, 1)) = 0
        _TimeSinceRipple("Time Since Ripple", Float) = 999
        _RippleMaxRadius("Ripple Max Radius", Float) = 5
        _RippleFadeTime("Ripple Fade Time", Float) = 2.0
    }
    SubShader
    {
        Tags { "RenderType"="Transparent" "Queue"="Transparent" "RenderPipeline"="UniversalPipeline" }
        
        Pass
        {
            // ShaderLab commands
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off
            Cull Off

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            // Includes
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            // This is crucial for accessing the screen texture behind the sprite.
            // Requires "Opaque Texture" to be enabled in the URP Asset.
            TEXTURE2D(_CameraOpaqueTexture);
            SAMPLER(sampler_CameraOpaqueTexture);

            // Properties
            CBUFFER_START(UnityPerMaterial)
                half4 _BaseColor;
                float _WaveAmplitude;
                float _WaveFrequency;
                float _WaveSpeed;
                float4 _CausticSpeed;
                float _CausticStrength;
                
                // Ripple Properties
                float4 _RippleCenter;
                float _RippleStrength;
                float _TimeSinceRipple;
                float _RippleMaxRadius;
                float _RippleFadeTime;
            CBUFFER_END

            TEXTURE2D(_CausticTex);
            SAMPLER(sampler_CausticTex);

            struct Attributes
            {
                float4 positionOS   : POSITION;
                float2 uv           : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionHCS  : SV_POSITION;
                float4 positionWS   : TEXCOORD0;
                float2 uv           : TEXCOORD1;
                float4 screenPos    : TEXCOORD2;
            };

            Varyings vert(Attributes IN)
            {
                Varyings OUT;
                OUT.positionWS = mul(unity_ObjectToWorld, IN.positionOS);
                
                // Sine wave displacement
                OUT.positionWS.y += sin((_Time.y * _WaveSpeed) + (OUT.positionWS.x * _WaveFrequency)) * _WaveAmplitude;

                OUT.positionHCS = TransformWorldToHClip(OUT.positionWS);
                OUT.uv = IN.uv;
                OUT.screenPos = ComputeScreenPos(OUT.positionHCS);
                return OUT;
            }

            half4 frag(Varyings IN) : SV_Target
            {
                // --- Caustics ---
                float2 causticUV = IN.uv + _Time.y * _CausticSpeed.xy;
                // CORRECTED: Using the proper macro for texture sampling
                half caustic = SAMPLE_TEXTURE2D(_CausticTex, sampler_CausticTex, causticUV).r * _CausticStrength;

                // --- Ripple Calculation ---
                float rippleEffect = 0;
                if (_TimeSinceRipple < _RippleFadeTime && _RippleStrength > 0)
                {
                    float dist = distance(IN.positionWS.xy, _RippleCenter.xy);
                    float currentRadius = (_TimeSinceRipple / _RippleFadeTime) * _RippleMaxRadius;
                    float rippleWave = 1 - saturate(abs(dist - currentRadius));
                    rippleWave = pow(rippleWave, 3.0);
                    float fade = 1.0 - saturate(_TimeSinceRipple / _RippleFadeTime);
                    rippleEffect = rippleWave * _RippleStrength * fade;
                }
                
                // --- Screen Color Refraction ---
                float2 screenUV = IN.screenPos.xy / IN.screenPos.w;
                screenUV.x += (caustic * 0.01) + (rippleEffect * 0.05); // Distort based on effects
                half4 refractedColor = SAMPLE_TEXTURE2D(_CameraOpaqueTexture, sampler_CameraOpaqueTexture, screenUV);

                // --- Final Color ---
                half4 finalColor = lerp(refractedColor, _BaseColor, _BaseColor.a);
                finalColor.rgb += caustic;
                finalColor.rgb += rippleEffect * _BaseColor.rgb * 2.0;

                return finalColor;
            }
            ENDHLSL
        }
    }
}